/* Base styles */
#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
  text-align: center;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: #000000;
}

body, html {
  margin: 0;
  padding: 0;
  background-color: #000000;
  color: white;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow: hidden;
}

/* Core layout */
.app-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.chat-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #000000;
}

/* Orb wrapper */
.orb-wrapper {
  width: 500px;
  height: 500px;
  position: relative;
  margin-bottom: 60px;
  background-color: #000000;
}

/* Centered orb wrapper for ImprovedChatInterface */
.orb-wrapper-centered {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(400px, 80vw);
  height: min(400px, 80vh);
  max-width: 500px;
  max-height: 500px;
  z-index: 10;
  overflow: visible;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .orb-wrapper-centered {
    width: min(300px, 90vw);
    height: min(300px, 60vh);
  }
}

@media (max-height: 600px) {
  .orb-wrapper-centered {
    width: min(250px, 70vw);
    height: min(250px, 70vh);
  }
}

/* Control buttons */
.control-buttons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.mic-button, .settings-button {
  padding: 12px;
  border-radius: 50%;
  background-color: #262626;
  position: relative;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, transform 0.1s;
}

.mic-button:hover, .settings-button:hover {
  background-color: #404040;
  transform: scale(1.05);
}

.mic-button:active, .settings-button:active {
  transform: scale(0.95);
}

/* User ID */
.user-id {
  color: white;
  margin-top: 16px;
  font-size: 14px;
  opacity: 0.7;
}

/* User speaking indicator */
.user-speaking-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #22c55e;
  border-radius: 50%;
}

/* Tailwind-like utility classes */
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
  background-color: #000000;
}

/* ReactBits-inspired Orb styles */
.orb-container {
  position: relative;
  width: 280px; 
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60px;
  z-index: 10;
}

.orb-glow {
  position: absolute;
  width: 280px;
  height: 280px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(96,165,250,0.6) 0%, rgba(37,99,235,0) 70%);
  filter: blur(25px);
  opacity: 0.8;
  z-index: 11;
}

.orb-main {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, #60a5fa 0%, #3b82f6 100%);
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  z-index: 12;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.orb-main.orb-active {
  animation: pulse 4s ease-in-out infinite;
}

.orb-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(circle, #1e40af 10%, #3b82f6 80%);
  z-index: 13;
}

/* Control buttons */
.control-buttons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.mic-button, .settings-button {
  padding: 12px;
  border-radius: 50%;
  background-color: #262626;
  position: relative;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, transform 0.1s;
}

.mic-button:hover, .settings-button:hover {
  background-color: #404040;
  transform: scale(1.05);
}

.mic-button:active, .settings-button:active {
  transform: scale(0.95);
}

/* User ID */
.user-id {
  color: white;
  margin-top: 16px;
  font-size: 14px;
  opacity: 0.7;
}

/* User speaking indicator */
.user-speaking-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #22c55e;
  border-radius: 50%;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 50px rgba(59, 130, 246, 0.8);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}