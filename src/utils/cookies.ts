
import Cookies from 'js-cookie';

export const getAuthCookies = (): { userId: string | undefined; serviceToken: string | undefined } => {
  // NOTE: Flutter InAppWebView needs to set these as *accessible* to JS,
  // meaning HttpOnly=false. This has security implications if the WebView
  // can navigate to arbitrary sites. Ensure the WebView is locked down.

  // For development/testing, fallback to hardcoded values if cookies are not available
  const userId = Cookies.get('userId') || (process.env.NODE_ENV === 'development' ? "riteshr24" : undefined);
  const serviceToken = Cookies.get('serviceToken') || (process.env.NODE_ENV === 'development' ? "240567" : undefined);

  return { userId, serviceToken };
}