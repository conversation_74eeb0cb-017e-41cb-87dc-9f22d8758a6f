// Global type declarations
import React from 'react';
import type { RealtimeSessionConnectionDetails } from 'gabber-client-core';

declare module 'lucide-react';

declare module 'gabber-client-react' {
  // Message interface for chat messages
  export interface ChatMessage {
    id: string;
    text: string;
    agent: boolean;
    final: boolean;
    timestamp?: number;
  }

  // Add userAudioLevel to session context
  export interface RealtimeSessionEngineContextData {
    /** Audio level of the local user, 0–1 */
    userAudioLevel?: number;
  }

  // Connection options interface
  export interface ConnectionOptions {
    connection_details: RealtimeSessionConnectionDetails;
  }

  /**
   * Hook to access real-time session engine context.
   */
  export function useRealtimeSessionEngine(): {
    /** Agent's current state */
    agentState: string;
    /** Agent's audio volume (0–1) */
    agentVolume?: number;
    /** Connection state */
    connectionState: string;
    /** Whether audio playback is available */
    canPlayAudio: boolean;
    /** Starts audio playback */
    startAudio: () => Promise<void>;
    /** Enables or disables the microphone */
    setMicrophoneEnabled: (enabled: boolean) => Promise<void>;
    /** Audio level of the local user, 0–1 */
    userAudioLevel?: number;
    /** Whether the microphone is enabled */
    microphoneEnabled: boolean;
    /** Array of chat messages */
    messages?: ChatMessage[];
  };

  export const RealtimeSessionEngineProvider: React.FC<{
    connectionOpts: ConnectionOptions;
    children?: React.ReactNode;
  }>;
}
